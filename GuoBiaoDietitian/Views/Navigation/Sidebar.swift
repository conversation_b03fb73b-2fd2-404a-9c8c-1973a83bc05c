/*
侧边栏导航

Abstract:
应用的侧边栏导航，参考 Food Truck 的 Sidebar 设计。
*/

import SwiftUI
import NutritionKit

struct Sidebar: View {
    @Binding var selection: Panel?
    @EnvironmentObject var nutritionModel: NutritionModel
    @EnvironmentObject var healthKitManager: HealthKitManager
    
    var body: some View {
        List(Panel.allCases, selection: $selection) { panel in
            NavigationLink(value: panel) {
                SidebarRow(panel: panel)
            }
        }
        .navigationTitle("国标营养师")
        #if os(iOS)
        .navigationBarTitleDisplayMode(.large)
        #endif
        .listStyle(.sidebar)
    }
}

struct SidebarRow: View {
    let panel: Panel
    @EnvironmentObject var nutritionModel: NutritionModel
    
    var body: some View {
        HStack {
            Image(systemName: panel.icon)
                .foregroundColor(.accentColor)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(panel.displayName)
                    .font(.headline)
                
                if let badge = badgeText {
                    Text(badge)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if let count = badgeCount {
                Text("\(count)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(.red, in: Capsule())
            }
        }
        .padding(.vertical, 4)
    }
    
    private var badgeText: String? {
        switch panel {
        case .overview:
            let summary = nutritionModel.todayNutritionSummary
            if let recommendation = summary.recommendation {
                let energyProgress = summary.totalNutrition.energyKcal / recommendation.energy
                return String(format: "%.0f%%", energyProgress * 100)
            }
            return nil
            
        case .record:
            return "今日 \(nutritionModel.todayRecords.count) 条记录"
            
        case .trends:
            return "过去7天数据"
            
        case .assistant:
            return "AI 建议"
            
        case .settings:
            return nil
        }
    }
    
    private var badgeCount: Int? {
        switch panel {
        case .record:
            // 显示今日未完成的营养目标数量
            let summary = nutritionModel.todayNutritionSummary
            guard let recommendation = summary.recommendation else { return nil }
            
            var incompleteGoals = 0
            if summary.totalNutrition.energyKcal < recommendation.energy * 0.8 {
                incompleteGoals += 1
            }
            if summary.totalNutrition.protein < recommendation.protein * 0.8 {
                incompleteGoals += 1
            }
            
            return incompleteGoals > 0 ? incompleteGoals : nil
            
        default:
            return nil
        }
    }
}

struct Sidebar_Previews: PreviewProvider {
    struct Preview: View {
        @State private var selection: Panel? = .overview
        
        var body: some View {
            NavigationSplitView {
                Sidebar(selection: $selection)
                    .environmentObject(NutritionModel.preview)
                    .environmentObject(HealthKitManager())
            } detail: {
                Text("Detail View")
            }
        }
    }
    
    static var previews: some View {
        Preview()
    }
}
