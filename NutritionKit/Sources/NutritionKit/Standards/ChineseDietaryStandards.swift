/*
中国膳食标准

Abstract:
基于《中国居民膳食指南 2022》和 GB 28050-2011 的营养标准和推荐量。
*/

import Foundation

public struct ChineseDietaryStandards {
    
    // MARK: - 营养素参考值 (NRV) - 基于 GB 28050-2011
    public static let nrvValues: [String: Double] = [
        "energy": 8400, // kJ
        "protein": 60, // g
        "fat": 60, // g
        "carbohydrate": 300, // g
        "sodium": 2000, // mg
        "vitaminA": 800, // μg RE
        "vitaminD": 10, // μg
        "vitaminE": 14, // mg α-TE
        "vitaminK": 80, // μg
        "vitaminC": 100, // mg
        "thiamine": 1.4, // mg
        "riboflavin": 1.4, // mg
        "niacin": 14, // mg
        "vitaminB6": 1.4, // mg
        "folate": 400, // μg
        "vitaminB12": 2.4, // μg
        "biotin": 30, // μg
        "pantothenicAcid": 5, // mg
        "calcium": 800, // mg
        "phosphorus": 700, // mg
        "potassium": 3500, // mg
        "magnesium": 300, // mg
        "iron": 15, // mg
        "zinc": 11.5, // mg
        "selenium": 60, // μg
        "copper": 1, // mg
        "manganese": 3, // mg
        "iodine": 150 // μg
    ]
    
    // MARK: - 每日推荐摄入量
    public struct DailyRecommendation {
        public let energy: Double // kcal
        public let protein: Double // g
        public let fat: Double // g
        public let carbohydrate: Double // g
        public let sodium: Double // mg
        public let dietaryFiber: Double // g
        public let water: Double // ml
        
        public init(
            energy: Double,
            protein: Double,
            fat: Double,
            carbohydrate: Double,
            sodium: Double,
            dietaryFiber: Double,
            water: Double
        ) {
            self.energy = energy
            self.protein = protein
            self.fat = fat
            self.carbohydrate = carbohydrate
            self.sodium = sodium
            self.dietaryFiber = dietaryFiber
            self.water = water
        }
    }
    
    // MARK: - 膳食推荐
    public struct DietaryRecommendations {
        
        // 根据性别和活动水平计算每日推荐量
        public static func dailyRecommendation(
            for gender: UserProfile.Gender,
            activityLevel: UserProfile.ActivityLevel,
            age: Int = 30,
            weight: Double = 65,
            height: Double = 170
        ) -> DailyRecommendation {
            
            // 基础代谢率计算 (Mifflin-St Jeor 公式)
            let bmr: Double
            switch gender {
            case .male:
                bmr = 10 * weight + 6.25 * height - 5 * Double(age) + 5
            case .female:
                bmr = 10 * weight + 6.25 * height - 5 * Double(age) - 161
            }
            
            // 总日消耗能量
            let tdee = bmr * activityLevel.multiplier
            
            // 蛋白质推荐量 (1.0-1.2 g/kg体重)
            let protein = weight * 1.1
            
            // 脂肪推荐量 (20-30% 总能量)
            let fatCalories = tdee * 0.25
            let fat = fatCalories / 9 // 1g脂肪 = 9kcal
            
            // 碳水化合物推荐量 (50-65% 总能量)
            let proteinCalories = protein * 4 // 1g蛋白质 = 4kcal
            let carbohydrateCalories = tdee - proteinCalories - fatCalories
            let carbohydrate = carbohydrateCalories / 4 // 1g碳水化合物 = 4kcal
            
            // 钠推荐量 (< 2300mg/天)
            let sodium: Double = 2000
            
            // 膳食纤维推荐量 (25-35g/天)
            let dietaryFiber: Double = 30
            
            // 水推荐量 (ml/天)
            let water = weight * 35 // 35ml/kg体重
            
            return DailyRecommendation(
                energy: tdee,
                protein: protein,
                fat: fat,
                carbohydrate: carbohydrate,
                sodium: sodium,
                dietaryFiber: dietaryFiber,
                water: water
            )
        }
        
        // 三餐能量分配比例
        public static let mealEnergyDistribution: [MealType: Double] = [
            .breakfast: 0.25,      // 早餐 25%
            .morningSnack: 0.05,   // 上午加餐 5%
            .lunch: 0.35,          // 午餐 35%
            .afternoonSnack: 0.05, // 下午加餐 5%
            .dinner: 0.25,         // 晚餐 25%
            .eveningSnack: 0.05    // 晚间加餐 5%
        ]
        
        // 食物组推荐量 (g/天)
        public static func foodGroupRecommendations(for energy: Double) -> [FoodCategory: ClosedRange<Double>] {
            // 基于2000kcal标准调整
            let ratio = energy / 2000.0
            
            return [
                .grains: (250 * ratio)...(400 * ratio),        // 谷薯类
                .vegetables: (300 * ratio)...(500 * ratio),    // 蔬菜类
                .fruits: (200 * ratio)...(350 * ratio),        // 水果类
                .meat: (120 * ratio)...(200 * ratio),          // 畜禽肉类
                .fish: (40 * ratio)...(75 * ratio),            // 鱼虾类
                .eggs: (40 * ratio)...(50 * ratio),            // 蛋类
                .dairy: (300 * ratio)...(500 * ratio),         // 奶类
                .nuts: (10 * ratio)...(15 * ratio),            // 坚果类
                .legumes: (25 * ratio)...(35 * ratio),         // 豆类
                .oils: (25 * ratio)...(30 * ratio)             // 油脂类
            ]
        }
    }
    
    // MARK: - 营养评估标准
    public struct NutritionAssessment {
        
        // 营养素摄入状态评估
        public static func assessNutrientIntake(
            actual: Double,
            recommended: Double,
            nutrient: String
        ) -> NutrientIntakeStatus {
            let ratio = actual / recommended
            
            switch nutrient {
            case "energy", "protein", "carbohydrate":
                if ratio < 0.8 { return .insufficient }
                else if ratio > 1.2 { return .excessive }
                else { return .adequate }
                
            case "fat":
                if ratio < 0.7 { return .insufficient }
                else if ratio > 1.3 { return .excessive }
                else { return .adequate }
                
            case "sodium":
                if ratio > 1.5 { return .excessive }
                else if ratio > 1.0 { return .borderlineHigh }
                else { return .adequate }
                
            default:
                if ratio < 0.7 { return .insufficient }
                else if ratio > 2.0 { return .excessive }
                else { return .adequate }
            }
        }
        
        // 整体膳食质量评估
        public static func assessDietQuality(
            energyRatio: Double,
            proteinRatio: Double,
            fatRatio: Double,
            carbohydrateRatio: Double,
            sodiumRatio: Double,
            fiberRatio: Double
        ) -> DietQuality {
            var score = 0
            
            // 能量评分
            if energyRatio >= 0.8 && energyRatio <= 1.2 { score += 20 }
            else if energyRatio >= 0.7 && energyRatio <= 1.3 { score += 15 }
            else if energyRatio >= 0.6 && energyRatio <= 1.4 { score += 10 }
            
            // 蛋白质评分
            if proteinRatio >= 0.8 && proteinRatio <= 1.2 { score += 20 }
            else if proteinRatio >= 0.7 && proteinRatio <= 1.3 { score += 15 }
            else if proteinRatio >= 0.6 && proteinRatio <= 1.4 { score += 10 }
            
            // 脂肪评分
            if fatRatio >= 0.7 && fatRatio <= 1.0 { score += 20 }
            else if fatRatio >= 0.6 && fatRatio <= 1.2 { score += 15 }
            else if fatRatio >= 0.5 && fatRatio <= 1.3 { score += 10 }
            
            // 碳水化合物评分
            if carbohydrateRatio >= 0.8 && carbohydrateRatio <= 1.2 { score += 20 }
            else if carbohydrateRatio >= 0.7 && carbohydrateRatio <= 1.3 { score += 15 }
            else if carbohydrateRatio >= 0.6 && carbohydrateRatio <= 1.4 { score += 10 }
            
            // 钠评分 (越低越好)
            if sodiumRatio <= 0.8 { score += 20 }
            else if sodiumRatio <= 1.0 { score += 15 }
            else if sodiumRatio <= 1.2 { score += 10 }
            else if sodiumRatio <= 1.5 { score += 5 }
            
            switch score {
            case 90...100: return .excellent
            case 80..<90: return .good
            case 70..<80: return .fair
            case 60..<70: return .poor
            default: return .veryPoor
            }
        }
    }
}

// MARK: - 营养素摄入状态
public enum NutrientIntakeStatus: String, CaseIterable {
    case insufficient = "不足"
    case adequate = "适宜"
    case borderlineHigh = "偏高"
    case excessive = "过量"
    
    public var color: String {
        switch self {
        case .insufficient: return "orange"
        case .adequate: return "green"
        case .borderlineHigh: return "yellow"
        case .excessive: return "red"
        }
    }
}

// MARK: - 膳食质量
public enum DietQuality: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "较差"
    case veryPoor = "很差"
    
    public var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .fair: return "yellow"
        case .poor: return "orange"
        case .veryPoor: return "red"
        }
    }
    
    public var description: String {
        switch self {
        case .excellent: return "膳食结构非常合理，营养均衡"
        case .good: return "膳食结构良好，略有改善空间"
        case .fair: return "膳食结构一般，需要调整"
        case .poor: return "膳食结构不合理，建议改善"
        case .veryPoor: return "膳食结构很差，急需改善"
        }
    }
}
