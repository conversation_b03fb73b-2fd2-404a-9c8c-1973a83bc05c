/*
Widget 视图组件

Abstract:
营养 Widget 的视图组件，提供不同尺寸的 Widget 界面。
*/

import SwiftUI
import WidgetKit

// MARK: - 今日营养摄入 Widget 视图
struct TodayNutritionWidgetView: View {
    let entry: NutritionEntry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallNutritionView(entry: entry)
        case .systemMedium:
            MediumNutritionView(entry: entry)
        case .systemLarge:
            LargeNutritionView(entry: entry)
        default:
            SmallNutritionView(entry: entry)
        }
    }
}

struct SmallNutritionView: View {
    let entry: NutritionEntry
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "heart.circle.fill")
                    .foregroundColor(.red)
                Spacer()
                Text("营养")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            // 能量进度环
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                
                Circle()
                    .trim(from: 0, to: entry.energyProgress)
                    .stroke(
                        LinearGradient(
                            colors: [.green, .yellow, .orange],
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                
                VStack(spacing: 2) {
                    Text("\(Int(entry.energyProgress * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("能量")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Text("剩余 \(entry.energyRemaining) kcal")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

struct MediumNutritionView: View {
    let entry: NutritionEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // 左侧：能量进度环
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 6)
                    
                    Circle()
                        .trim(from: 0, to: entry.energyProgress)
                        .stroke(
                            LinearGradient(
                                colors: [.green, .yellow, .orange],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 6, lineCap: .round)
                        )
                        .rotationEffect(.degrees(-90))
                    
                    VStack(spacing: 1) {
                        Text("\(Int(entry.energyProgress * 100))%")
                            .font(.title3)
                            .fontWeight(.bold)
                        
                        Text("能量")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(width: 80, height: 80)
                
                Text("剩余 \(entry.energyRemaining)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 右侧：营养素进度条
            VStack(alignment: .leading, spacing: 12) {
                Text("营养摄入")
                    .font(.headline)
                    .fontWeight(.medium)
                
                NutrientProgressBar(
                    name: "蛋白质",
                    progress: entry.proteinProgress,
                    color: .blue
                )
                
                NutrientProgressBar(
                    name: "脂肪",
                    progress: entry.fatProgress,
                    color: .orange
                )
                
                NutrientProgressBar(
                    name: "碳水",
                    progress: entry.carbohydrateProgress,
                    color: .green
                )
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

struct LargeNutritionView: View {
    let entry: NutritionEntry
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "heart.circle.fill")
                    .foregroundColor(.red)
                    .font(.title2)
                
                Text("今日营养摄入")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text(entry.date, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 能量概览
            HStack(spacing: 20) {
                // 能量进度环
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                    
                    Circle()
                        .trim(from: 0, to: entry.energyProgress)
                        .stroke(
                            LinearGradient(
                                colors: [.green, .yellow, .orange],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 8, lineCap: .round)
                        )
                        .rotationEffect(.degrees(-90))
                    
                    VStack(spacing: 2) {
                        Text("\(Int(entry.energyProgress * 100))%")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("能量")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(width: 100, height: 100)
                
                // 能量数据
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("已摄入:")
                        Spacer()
                        Text("\(entry.totalEnergy - entry.energyRemaining) kcal")
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Text("剩余:")
                        Spacer()
                        Text("\(entry.energyRemaining) kcal")
                            .fontWeight(.medium)
                            .foregroundColor(entry.energyRemaining > 0 ? .green : .red)
                    }
                    
                    HStack {
                        Text("目标:")
                        Spacer()
                        Text("\(entry.totalEnergy) kcal")
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                    }
                }
                .font(.subheadline)
            }
            
            // 营养素进度
            VStack(alignment: .leading, spacing: 10) {
                Text("营养素摄入进度")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                NutrientProgressBar(
                    name: "蛋白质",
                    progress: entry.proteinProgress,
                    color: .blue
                )
                
                NutrientProgressBar(
                    name: "脂肪",
                    progress: entry.fatProgress,
                    color: .orange
                )
                
                NutrientProgressBar(
                    name: "碳水化合物",
                    progress: entry.carbohydrateProgress,
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - 卡路里余额 Widget 视图
struct CalorieBalanceWidgetView: View {
    let entry: CalorieEntry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallCalorieView(entry: entry)
        case .systemMedium:
            MediumCalorieView(entry: entry)
        default:
            SmallCalorieView(entry: entry)
        }
    }
}

struct SmallCalorieView: View {
    let entry: CalorieEntry
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                Spacer()
                Text("卡路里")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text("\(entry.remaining)")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(entry.remaining > 0 ? .green : .red)
                
                Text("剩余 kcal")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("\(entry.consumed)/\(entry.total)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

struct MediumCalorieView: View {
    let entry: CalorieEntry
    
    var body: some View {
        HStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)
                    Text("卡路里余额")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                
                Text("\(entry.remaining)")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(entry.remaining > 0 ? .green : .red)
                
                Text("剩余 kcal")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 8) {
                VStack(alignment: .trailing, spacing: 4) {
                    Text("已摄入")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(entry.consumed)")
                        .font(.title2)
                        .fontWeight(.medium)
                }
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("目标")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(entry.total)")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - 辅助组件
struct NutrientProgressBar: View {
    let name: String
    let progress: Double
    let color: Color
    
    var body: some View {
        HStack(spacing: 8) {
            Text(name)
                .font(.caption)
                .frame(width: 40, alignment: .leading)
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * progress, height: 6)
                        .cornerRadius(3)
                }
            }
            .frame(height: 6)
            
            Text("\(Int(progress * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

// MARK: - iOS 专用 Widget 视图
#if os(iOS)
struct NutritionProgressWidgetView: View {
    let entry: NutritionEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.blue)
                Text("营养进度")
                    .font(.headline)
                    .fontWeight(.medium)
                Spacer()
            }
            
            NutrientProgressBar(
                name: "蛋白质",
                progress: entry.proteinProgress,
                color: .blue
            )
            
            NutrientProgressBar(
                name: "脂肪",
                progress: entry.fatProgress,
                color: .orange
            )
            
            NutrientProgressBar(
                name: "碳水化合物",
                progress: entry.carbohydrateProgress,
                color: .green
            )
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

struct WaterIntakeWidgetView: View {
    let entry: WaterEntry
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "drop.fill")
                    .foregroundColor(.blue)
                Spacer()
                Text("水分")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text("\(entry.consumed)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
                
                Text("/ \(entry.target) ml")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 水分进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [.blue.opacity(0.7), .blue],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * Double(entry.consumed) / Double(entry.target))
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
}
#endif
