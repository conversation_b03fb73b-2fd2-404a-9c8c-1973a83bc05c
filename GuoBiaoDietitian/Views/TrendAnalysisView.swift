import SwiftUI
import Charts

struct TrendAnalysisView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @State private var selectedTimeRange: TimeRange = .week
    @State private var selectedMetric: NutritionMetric = .energy
    
    enum TimeRange: String, CaseIterable {
        case week = "本周"
        case month = "本月"
        case threeMonths = "三个月"
    }
    
    enum NutritionMetric: String, CaseIterable {
        case energy = "能量"
        case protein = "蛋白质"
        case fat = "脂肪"
        case carbohydrate = "碳水化合物"
        case sodium = "钠"
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 时间范围选择
                    TimeRangeSelector(selectedTimeRange: $selectedTimeRange)
                    
                    // 营养指标选择
                    MetricSelector(selectedMetric: $selectedMetric)
                    
                    // 趋势图表
                    TrendChartView(
                        timeRange: selectedTimeRange,
                        metric: selectedMetric
                    )
                    
                    // 统计摘要
                    TrendSummaryView(
                        timeRange: selectedTimeRange,
                        metric: selectedMetric
                    )
                    
                    // 营养目标达成情况
                    GoalAchievementView(timeRange: selectedTimeRange)
                    
                    Spacer(minLength: 100)
                }
                .padding()
            }
            .navigationTitle("趋势分析")
            .onAppear {
                nutritionManager.loadWeeklyRecords()
                nutritionManager.loadMonthlyRecords()
            }
        }
    }
}

struct TimeRangeSelector: View {
    @Binding var selectedTimeRange: TrendAnalysisView.TimeRange
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("时间范围")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                ForEach(TrendAnalysisView.TimeRange.allCases, id: \.self) { range in
                    Button(action: {
                        selectedTimeRange = range
                    }) {
                        Text(range.rawValue)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedTimeRange == range ?
                                Color.blue : Color(.systemGray5)
                            )
                            .foregroundColor(
                                selectedTimeRange == range ?
                                .white : .primary
                            )
                            .cornerRadius(20)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct MetricSelector: View {
    @Binding var selectedMetric: TrendAnalysisView.NutritionMetric
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("营养指标")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(TrendAnalysisView.NutritionMetric.allCases, id: \.self) { metric in
                        Button(action: {
                            selectedMetric = metric
                        }) {
                            Text(metric.rawValue)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    selectedMetric == metric ?
                                    Color.green : Color(.systemGray5)
                                )
                                .foregroundColor(
                                    selectedMetric == metric ?
                                    .white : .primary
                                )
                                .cornerRadius(20)
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct TrendChartView: View {
    let timeRange: TrendAnalysisView.TimeRange
    let metric: TrendAnalysisView.NutritionMetric
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var chartData: [DailyNutritionData] {
        // 这里应该根据 timeRange 和 metric 生成实际的图表数据
        // 目前返回模拟数据
        return generateMockData()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(metric.rawValue)趋势")
                .font(.headline)
                .fontWeight(.semibold)
            
            if #available(iOS 16.0, *) {
                Chart(chartData) { data in
                    LineMark(
                        x: .value("日期", data.date),
                        y: .value(metric.rawValue, data.getValue(for: metric))
                    )
                    .foregroundStyle(getColorForMetric(metric))
                    .lineStyle(StrokeStyle(lineWidth: 3))
                    
                    PointMark(
                        x: .value("日期", data.date),
                        y: .value(metric.rawValue, data.getValue(for: metric))
                    )
                    .foregroundStyle(getColorForMetric(metric))
                    .symbolSize(50)
                }
                .frame(height: 200)
                .chartYAxis {
                    AxisMarks(position: .leading)
                }
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day)) { _ in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
            } else {
                // iOS 15 及以下版本的替代方案
                VStack {
                    Text("图表功能需要 iOS 16+")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // 简单的数据列表显示
                    ForEach(chartData.prefix(5)) { data in
                        HStack {
                            Text(data.date, style: .date)
                                .font(.caption)
                            
                            Spacer()
                            
                            Text("\(Int(data.getValue(for: metric))) \(getUnitForMetric(metric))")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .padding(.vertical, 4)
                    }
                }
                .frame(height: 200)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private func getColorForMetric(_ metric: TrendAnalysisView.NutritionMetric) -> Color {
        switch metric {
        case .energy: return .orange
        case .protein: return .blue
        case .fat: return .yellow
        case .carbohydrate: return .green
        case .sodium: return .red
        }
    }
    
    private func getUnitForMetric(_ metric: TrendAnalysisView.NutritionMetric) -> String {
        switch metric {
        case .energy: return "kcal"
        case .protein, .fat, .carbohydrate: return "g"
        case .sodium: return "mg"
        }
    }
    
    private func generateMockData() -> [DailyNutritionData] {
        let calendar = Calendar.current
        let today = Date()
        var data: [DailyNutritionData] = []
        
        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                data.append(DailyNutritionData(
                    date: date,
                    energy: Double.random(in: 1500...2500),
                    protein: Double.random(in: 50...100),
                    fat: Double.random(in: 40...80),
                    carbohydrate: Double.random(in: 200...350),
                    sodium: Double.random(in: 1000...3000)
                ))
            }
        }
        
        return data.reversed()
    }
}

struct DailyNutritionData: Identifiable {
    let id = UUID()
    let date: Date
    let energy: Double
    let protein: Double
    let fat: Double
    let carbohydrate: Double
    let sodium: Double
    
    func getValue(for metric: TrendAnalysisView.NutritionMetric) -> Double {
        switch metric {
        case .energy: return energy
        case .protein: return protein
        case .fat: return fat
        case .carbohydrate: return carbohydrate
        case .sodium: return sodium
        }
    }
}

struct TrendSummaryView: View {
    let timeRange: TrendAnalysisView.TimeRange
    let metric: TrendAnalysisView.NutritionMetric
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(timeRange.rawValue)统计")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
                StatisticItem(
                    title: "平均值",
                    value: "1,850",
                    unit: getUnitForMetric(metric),
                    color: .blue
                )
                
                StatisticItem(
                    title: "最高值",
                    value: "2,340",
                    unit: getUnitForMetric(metric),
                    color: .green
                )
                
                StatisticItem(
                    title: "最低值",
                    value: "1,420",
                    unit: getUnitForMetric(metric),
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private func getUnitForMetric(_ metric: TrendAnalysisView.NutritionMetric) -> String {
        switch metric {
        case .energy: return "kcal"
        case .protein, .fat, .carbohydrate: return "g"
        case .sodium: return "mg"
        }
    }
}

struct StatisticItem: View {
    let title: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(unit)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct GoalAchievementView: View {
    let timeRange: TrendAnalysisView.TimeRange
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("目标达成情况")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                GoalProgressItem(
                    name: "能量目标",
                    achieved: 5,
                    total: 7,
                    color: .orange
                )
                
                GoalProgressItem(
                    name: "蛋白质目标",
                    achieved: 6,
                    total: 7,
                    color: .blue
                )
                
                GoalProgressItem(
                    name: "营养均衡",
                    achieved: 4,
                    total: 7,
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct GoalProgressItem: View {
    let name: String
    let achieved: Int
    let total: Int
    let color: Color
    
    private var progress: Double {
        guard total > 0 else { return 0 }
        return Double(achieved) / Double(total)
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(achieved)/\(total) 天达成")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int(progress * 100))%")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(color)
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: color))
                    .frame(width: 80)
            }
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    TrendAnalysisView()
        .environmentObject(NutritionManager())
}
