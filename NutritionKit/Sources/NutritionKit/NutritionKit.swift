/*
NutritionKit - 营养管理核心框架

Abstract:
基于中国国标的营养管理核心业务逻辑包，提供营养计算、数据模型和分析功能。
*/

import Foundation
import SwiftUI
import Combine

// MARK: - Public API
public struct NutritionKit {
    public static let version = "1.0.0"
    
    /// 初始化 NutritionKit
    public static func initialize() {
        // 初始化配置
        print("NutritionKit \(version) initialized")
    }
}

// MARK: - 导出主要模块
// 这些类型将自动可用，无需显式导入
