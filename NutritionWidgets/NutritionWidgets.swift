/*
营养 Widget 扩展

Abstract:
为营养师应用提供 Widget 支持，包括今日营养摄入进度、卡路里余额等。
*/

import WidgetKit
import SwiftUI

@main
struct NutritionWidgets: WidgetBundle {
    var body: some Widget {
        // MARK: - 主要 Widgets
        #if os(iOS) || os(macOS)
        TodayNutritionWidget()
        CalorieBalanceWidget()
        #endif
        
        // MARK: - 辅助 Widgets (仅 iOS)
        #if os(iOS)
        NutritionProgressWidget()
        WaterIntakeWidget()
        #endif
    }
}

// MARK: - 今日营养摄入 Widget
struct TodayNutritionWidget: Widget {
    let kind: String = "TodayNutritionWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: NutritionProvider()) { entry in
            TodayNutritionWidgetView(entry: entry)
        }
        .configurationDisplayName("今日营养摄入")
        .description("查看今日营养摄入进度和健康状态")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

// MARK: - 卡路里余额 Widget
struct CalorieBalanceWidget: Widget {
    let kind: String = "CalorieBalanceWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: CalorieProvider()) { entry in
            CalorieBalanceWidgetView(entry: entry)
        }
        .configurationDisplayName("卡路里余额")
        .description("显示今日剩余可摄入卡路里")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

// MARK: - 营养进度 Widget (仅 iOS)
#if os(iOS)
struct NutritionProgressWidget: Widget {
    let kind: String = "NutritionProgressWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: NutritionProvider()) { entry in
            NutritionProgressWidgetView(entry: entry)
        }
        .configurationDisplayName("营养进度")
        .description("显示蛋白质、脂肪、碳水化合物摄入进度")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

struct WaterIntakeWidget: Widget {
    let kind: String = "WaterIntakeWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: WaterProvider()) { entry in
            WaterIntakeWidgetView(entry: entry)
        }
        .configurationDisplayName("水分摄入")
        .description("追踪今日水分摄入量")
        .supportedFamilies([.systemSmall])
    }
}
#endif

// MARK: - Widget 数据提供者
struct NutritionProvider: TimelineProvider {
    func placeholder(in context: Context) -> NutritionEntry {
        NutritionEntry(
            date: Date(),
            energyProgress: 0.65,
            proteinProgress: 0.8,
            fatProgress: 0.45,
            carbohydrateProgress: 0.7,
            energyRemaining: 800,
            totalEnergy: 2000
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (NutritionEntry) -> ()) {
        let entry = NutritionEntry(
            date: Date(),
            energyProgress: 0.65,
            proteinProgress: 0.8,
            fatProgress: 0.45,
            carbohydrateProgress: 0.7,
            energyRemaining: 800,
            totalEnergy: 2000
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<NutritionEntry>) -> ()) {
        // 在实际应用中，这里会从 App Group 或 Core Data 读取数据
        let currentDate = Date()
        let entry = NutritionEntry(
            date: currentDate,
            energyProgress: 0.65,
            proteinProgress: 0.8,
            fatProgress: 0.45,
            carbohydrateProgress: 0.7,
            energyRemaining: 800,
            totalEnergy: 2000
        )
        
        // 每小时更新一次
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
}

struct CalorieProvider: TimelineProvider {
    func placeholder(in context: Context) -> CalorieEntry {
        CalorieEntry(date: Date(), consumed: 1200, remaining: 800, total: 2000)
    }
    
    func getSnapshot(in context: Context, completion: @escaping (CalorieEntry) -> ()) {
        let entry = CalorieEntry(date: Date(), consumed: 1200, remaining: 800, total: 2000)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<CalorieEntry>) -> ()) {
        let currentDate = Date()
        let entry = CalorieEntry(date: currentDate, consumed: 1200, remaining: 800, total: 2000)
        
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
}

#if os(iOS)
struct WaterProvider: TimelineProvider {
    func placeholder(in context: Context) -> WaterEntry {
        WaterEntry(date: Date(), consumed: 1500, target: 2000)
    }
    
    func getSnapshot(in context: Context, completion: @escaping (WaterEntry) -> ()) {
        let entry = WaterEntry(date: Date(), consumed: 1500, target: 2000)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<WaterEntry>) -> ()) {
        let currentDate = Date()
        let entry = WaterEntry(date: currentDate, consumed: 1500, target: 2000)
        
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
}
#endif

// MARK: - Widget 数据模型
struct NutritionEntry: TimelineEntry {
    let date: Date
    let energyProgress: Double
    let proteinProgress: Double
    let fatProgress: Double
    let carbohydrateProgress: Double
    let energyRemaining: Int
    let totalEnergy: Int
}

struct CalorieEntry: TimelineEntry {
    let date: Date
    let consumed: Int
    let remaining: Int
    let total: Int
}

#if os(iOS)
struct WaterEntry: TimelineEntry {
    let date: Date
    let consumed: Int // ml
    let target: Int // ml
}
#endif
