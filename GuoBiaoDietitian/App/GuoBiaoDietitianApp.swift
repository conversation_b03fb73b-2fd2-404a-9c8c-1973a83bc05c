import SwiftUI
import HealthKit
import NutritionKit

@main
struct GuoBiaoDietitianApp: App {
    @StateObject private var healthKitManager = HealthKitManager()
    @StateObject private var nutritionModel = NutritionModel()
    @StateObject private var liveActivityManager = LiveActivityManager()

    var body: some Scene {
        WindowGroup {
            ContentView(model: nutritionModel, healthKitManager: healthKitManager)
                .environmentObject(liveActivityManager)
                .onAppear {
                    liveActivityManager.setNutritionModel(nutritionModel)
                    liveActivityManager.checkAutoStart()
                }
        }
        #if os(macOS)
        .defaultSize(width: 1200, height: 800)
        #endif

        #if os(macOS)
        MenuBarExtra {
            ScrollView {
                VStack(spacing: 0) {
                    Text("国标营养师")
                        .font(.headline)
                        .padding()

                    if let summary = nutritionModel.todayNutritionSummary.recommendation {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("今日营养摄入")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            HStack {
                                Text("能量:")
                                Spacer()
                                Text("\(Int(nutritionModel.todayNutritionSummary.totalNutrition.energyKcal))/\(Int(summary.energy)) kcal")
                            }
                            .font(.caption)
                        }
                        .padding(.horizontal)
                    }
                }
            }
        } label: {
            Label("国标营养师", systemImage: "heart.circle")
        }
        .menuBarExtraStyle(.window)
        #endif
    }
}
