/*
营养数据模型

Abstract:
定义营养成分、食物、食物记录等核心数据模型，遵循中国国标 GB 28050-2011。
*/

import Foundation
import SwiftUI

// MARK: - 营养成分模型 (基于 GB 28050-2011)
public struct NutritionFacts: Codable, Hashable {
    // 必需标示的营养成分
    public let energy: Double // 能量 (kJ)
    public let protein: Double // 蛋白质 (g)
    public let fat: Double // 脂肪 (g)
    public let carbohydrate: Double // 碳水化合物 (g)
    public let sodium: Double // 钠 (mg)
    
    // 可选标示的营养成分
    public let saturatedFat: Double? // 饱和脂肪 (g)
    public let transFat: Double? // 反式脂肪 (g)
    public let cholesterol: Double? // 胆固醇 (mg)
    public let dietaryFiber: Double? // 膳食纤维 (g)
    public let sugars: Double? // 糖 (g)
    
    // 维生素
    public let vitaminA: Double? // 维生素A (μg RE)
    public let vitaminD: Double? // 维生素D (μg)
    public let vitaminE: Double? // 维生素E (mg α-TE)
    public let vitaminK: Double? // 维生素K (μg)
    public let vitaminC: Double? // 维生素C (mg)
    public let thiamine: Double? // 硫胺素(维生素B1) (mg)
    public let riboflavin: Double? // 核黄素(维生素B2) (mg)
    public let niacin: Double? // 烟酸 (mg)
    public let vitaminB6: Double? // 维生素B6 (mg)
    public let folate: Double? // 叶酸 (μg)
    public let vitaminB12: Double? // 维生素B12 (μg)
    public let biotin: Double? // 生物素 (μg)
    public let pantothenicAcid: Double? // 泛酸 (mg)
    
    // 矿物质
    public let calcium: Double? // 钙 (mg)
    public let phosphorus: Double? // 磷 (mg)
    public let potassium: Double? // 钾 (mg)
    public let magnesium: Double? // 镁 (mg)
    public let iron: Double? // 铁 (mg)
    public let zinc: Double? // 锌 (mg)
    public let selenium: Double? // 硒 (μg)
    public let copper: Double? // 铜 (mg)
    public let manganese: Double? // 锰 (mg)
    public let iodine: Double? // 碘 (μg)
    
    public init(
        energy: Double,
        protein: Double,
        fat: Double,
        carbohydrate: Double,
        sodium: Double,
        saturatedFat: Double? = nil,
        transFat: Double? = nil,
        cholesterol: Double? = nil,
        dietaryFiber: Double? = nil,
        sugars: Double? = nil,
        vitaminA: Double? = nil,
        vitaminD: Double? = nil,
        vitaminE: Double? = nil,
        vitaminK: Double? = nil,
        vitaminC: Double? = nil,
        thiamine: Double? = nil,
        riboflavin: Double? = nil,
        niacin: Double? = nil,
        vitaminB6: Double? = nil,
        folate: Double? = nil,
        vitaminB12: Double? = nil,
        biotin: Double? = nil,
        pantothenicAcid: Double? = nil,
        calcium: Double? = nil,
        phosphorus: Double? = nil,
        potassium: Double? = nil,
        magnesium: Double? = nil,
        iron: Double? = nil,
        zinc: Double? = nil,
        selenium: Double? = nil,
        copper: Double? = nil,
        manganese: Double? = nil,
        iodine: Double? = nil
    ) {
        self.energy = energy
        self.protein = protein
        self.fat = fat
        self.carbohydrate = carbohydrate
        self.sodium = sodium
        self.saturatedFat = saturatedFat
        self.transFat = transFat
        self.cholesterol = cholesterol
        self.dietaryFiber = dietaryFiber
        self.sugars = sugars
        self.vitaminA = vitaminA
        self.vitaminD = vitaminD
        self.vitaminE = vitaminE
        self.vitaminK = vitaminK
        self.vitaminC = vitaminC
        self.thiamine = thiamine
        self.riboflavin = riboflavin
        self.niacin = niacin
        self.vitaminB6 = vitaminB6
        self.folate = folate
        self.vitaminB12 = vitaminB12
        self.biotin = biotin
        self.pantothenicAcid = pantothenicAcid
        self.calcium = calcium
        self.phosphorus = phosphorus
        self.potassium = potassium
        self.magnesium = magnesium
        self.iron = iron
        self.zinc = zinc
        self.selenium = selenium
        self.copper = copper
        self.manganese = manganese
        self.iodine = iodine
    }
}

public extension NutritionFacts {
    // 计算能量 (kcal)
    var energyKcal: Double {
        return energy / 4.184 // 1 kcal = 4.184 kJ
    }
    
    // 计算营养素参考值百分比 (%NRV)
    func nrvPercentage(for nutrient: String, amount: Double) -> Double? {
        guard let nrv = ChineseDietaryStandards.nrvValues[nutrient] else { return nil }
        return (amount / nrv) * 100
    }
    
    // 营养成分相加
    static func + (lhs: NutritionFacts, rhs: NutritionFacts) -> NutritionFacts {
        return NutritionFacts(
            energy: lhs.energy + rhs.energy,
            protein: lhs.protein + rhs.protein,
            fat: lhs.fat + rhs.fat,
            carbohydrate: lhs.carbohydrate + rhs.carbohydrate,
            sodium: lhs.sodium + rhs.sodium,
            saturatedFat: addOptional(lhs.saturatedFat, rhs.saturatedFat),
            transFat: addOptional(lhs.transFat, rhs.transFat),
            cholesterol: addOptional(lhs.cholesterol, rhs.cholesterol),
            dietaryFiber: addOptional(lhs.dietaryFiber, rhs.dietaryFiber),
            sugars: addOptional(lhs.sugars, rhs.sugars),
            vitaminA: addOptional(lhs.vitaminA, rhs.vitaminA),
            vitaminD: addOptional(lhs.vitaminD, rhs.vitaminD),
            vitaminE: addOptional(lhs.vitaminE, rhs.vitaminE),
            vitaminK: addOptional(lhs.vitaminK, rhs.vitaminK),
            vitaminC: addOptional(lhs.vitaminC, rhs.vitaminC),
            thiamine: addOptional(lhs.thiamine, rhs.thiamine),
            riboflavin: addOptional(lhs.riboflavin, rhs.riboflavin),
            niacin: addOptional(lhs.niacin, rhs.niacin),
            vitaminB6: addOptional(lhs.vitaminB6, rhs.vitaminB6),
            folate: addOptional(lhs.folate, rhs.folate),
            vitaminB12: addOptional(lhs.vitaminB12, rhs.vitaminB12),
            biotin: addOptional(lhs.biotin, rhs.biotin),
            pantothenicAcid: addOptional(lhs.pantothenicAcid, rhs.pantothenicAcid),
            calcium: addOptional(lhs.calcium, rhs.calcium),
            phosphorus: addOptional(lhs.phosphorus, rhs.phosphorus),
            potassium: addOptional(lhs.potassium, rhs.potassium),
            magnesium: addOptional(lhs.magnesium, rhs.magnesium),
            iron: addOptional(lhs.iron, rhs.iron),
            zinc: addOptional(lhs.zinc, rhs.zinc),
            selenium: addOptional(lhs.selenium, rhs.selenium),
            copper: addOptional(lhs.copper, rhs.copper),
            manganese: addOptional(lhs.manganese, rhs.manganese),
            iodine: addOptional(lhs.iodine, rhs.iodine)
        )
    }
    
    private static func addOptional(_ lhs: Double?, _ rhs: Double?) -> Double? {
        switch (lhs, rhs) {
        case (let l?, let r?): return l + r
        case (let l?, nil): return l
        case (nil, let r?): return r
        case (nil, nil): return nil
        }
    }
    
    // 空营养成分
    static var empty: NutritionFacts {
        return NutritionFacts(
            energy: 0,
            protein: 0,
            fat: 0,
            carbohydrate: 0,
            sodium: 0
        )
    }
}

// MARK: - 食物分类
public enum FoodCategory: String, CaseIterable, Codable {
    case grains = "谷薯类"
    case vegetables = "蔬菜类"
    case fruits = "水果类"
    case meat = "畜禽肉类"
    case fish = "鱼虾类"
    case eggs = "蛋类"
    case dairy = "奶类"
    case nuts = "坚果类"
    case legumes = "豆类"
    case oils = "油脂类"
    case beverages = "饮料类"
    case snacks = "零食类"
    case condiments = "调料类"
    case processed = "加工食品"
    case other = "其他"
}

// MARK: - 食物来源
public enum FoodSource: String, Codable {
    case database = "数据库"
    case userInput = "用户输入"
    case barcode = "条码扫描"
    case photo = "拍照识别"
    case voice = "语音输入"
}

// MARK: - 食物模型
public struct Food: Codable, Identifiable, Hashable {
    public let id: UUID
    public let name: String
    public let brand: String?
    public let barcode: String?
    public let category: FoodCategory
    public let nutritionFacts: NutritionFacts // 每100g的营养成分
    public let servingSize: Double? // 建议食用量 (g)
    public let imageURL: String?
    public let source: FoodSource
    public let createdAt: Date
    public let updatedAt: Date
    
    public init(
        id: UUID = UUID(),
        name: String,
        brand: String? = nil,
        barcode: String? = nil,
        category: FoodCategory,
        nutritionFacts: NutritionFacts,
        servingSize: Double? = nil,
        imageURL: String? = nil,
        source: FoodSource,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.brand = brand
        self.barcode = barcode
        self.category = category
        self.nutritionFacts = nutritionFacts
        self.servingSize = servingSize
        self.imageURL = imageURL
        self.source = source
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}
