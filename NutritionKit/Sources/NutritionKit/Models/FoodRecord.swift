/*
食物记录模型

Abstract:
定义食物记录、用餐类型等相关数据模型。
*/

import Foundation

// MARK: - 用餐类型
public enum MealType: String, CaseIterable, Codable {
    case breakfast = "早餐"
    case morningSnack = "上午加餐"
    case lunch = "午餐"
    case afternoonSnack = "下午加餐"
    case dinner = "晚餐"
    case eveningSnack = "晚间加餐"
    
    public var displayName: String {
        return self.rawValue
    }
    
    public var icon: String {
        switch self {
        case .breakfast: return "sunrise.fill"
        case .morningSnack: return "cup.and.saucer.fill"
        case .lunch: return "sun.max.fill"
        case .afternoonSnack: return "leaf.fill"
        case .dinner: return "moon.fill"
        case .eveningSnack: return "moon.stars.fill"
        }
    }
}

// MARK: - 食物记录
public struct FoodRecord: Codable, Identifiable, Hashable {
    public let id: UUID
    public let food: Food
    public let amount: Double // 实际食用量 (g)
    public let mealType: MealType
    public let timestamp: Date
    public let notes: String?

    public init(
        id: UUID = UUID(),
        food: Food,
        amount: Double,
        mealType: MealType,
        timestamp: Date = Date(),
        notes: String? = nil
    ) {
        self.id = id
        self.food = food
        self.amount = amount
        self.mealType = mealType
        self.timestamp = timestamp
        self.notes = notes
    }
}

public extension FoodRecord {
    // 计算实际营养成分 (基于食用量)
    var actualNutrition: NutritionFacts {
        let ratio = amount / 100.0 // 营养成分基于每100g
        
        return NutritionFacts(
            energy: food.nutritionFacts.energy * ratio,
            protein: food.nutritionFacts.protein * ratio,
            fat: food.nutritionFacts.fat * ratio,
            carbohydrate: food.nutritionFacts.carbohydrate * ratio,
            sodium: food.nutritionFacts.sodium * ratio,
            saturatedFat: food.nutritionFacts.saturatedFat.map { $0 * ratio },
            transFat: food.nutritionFacts.transFat.map { $0 * ratio },
            cholesterol: food.nutritionFacts.cholesterol.map { $0 * ratio },
            dietaryFiber: food.nutritionFacts.dietaryFiber.map { $0 * ratio },
            sugars: food.nutritionFacts.sugars.map { $0 * ratio },
            vitaminA: food.nutritionFacts.vitaminA.map { $0 * ratio },
            vitaminD: food.nutritionFacts.vitaminD.map { $0 * ratio },
            vitaminE: food.nutritionFacts.vitaminE.map { $0 * ratio },
            vitaminK: food.nutritionFacts.vitaminK.map { $0 * ratio },
            vitaminC: food.nutritionFacts.vitaminC.map { $0 * ratio },
            thiamine: food.nutritionFacts.thiamine.map { $0 * ratio },
            riboflavin: food.nutritionFacts.riboflavin.map { $0 * ratio },
            niacin: food.nutritionFacts.niacin.map { $0 * ratio },
            vitaminB6: food.nutritionFacts.vitaminB6.map { $0 * ratio },
            folate: food.nutritionFacts.folate.map { $0 * ratio },
            vitaminB12: food.nutritionFacts.vitaminB12.map { $0 * ratio },
            biotin: food.nutritionFacts.biotin.map { $0 * ratio },
            pantothenicAcid: food.nutritionFacts.pantothenicAcid.map { $0 * ratio },
            calcium: food.nutritionFacts.calcium.map { $0 * ratio },
            phosphorus: food.nutritionFacts.phosphorus.map { $0 * ratio },
            potassium: food.nutritionFacts.potassium.map { $0 * ratio },
            magnesium: food.nutritionFacts.magnesium.map { $0 * ratio },
            iron: food.nutritionFacts.iron.map { $0 * ratio },
            zinc: food.nutritionFacts.zinc.map { $0 * ratio },
            selenium: food.nutritionFacts.selenium.map { $0 * ratio },
            copper: food.nutritionFacts.copper.map { $0 * ratio },
            manganese: food.nutritionFacts.manganese.map { $0 * ratio },
            iodine: food.nutritionFacts.iodine.map { $0 * ratio }
        )
    }
    
    // 是否为今天的记录
    var isToday: Bool {
        Calendar.current.isDateInToday(timestamp)
    }
    
    // 格式化时间显示
    var timeString: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
    
    // 格式化日期显示
    var dateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: timestamp)
    }
}

// MARK: - 营养摘要
public struct NutritionSummary {
    public let totalNutrition: NutritionFacts
    public let recommendation: ChineseDietaryStandards.DailyRecommendation?
    public let mealDistribution: [MealType: Double]
    public let recordsCount: Int
    
    public init(
        totalNutrition: NutritionFacts,
        recommendation: ChineseDietaryStandards.DailyRecommendation? = nil,
        mealDistribution: [MealType: Double] = [:],
        recordsCount: Int = 0
    ) {
        self.totalNutrition = totalNutrition
        self.recommendation = recommendation
        self.mealDistribution = mealDistribution
        self.recordsCount = recordsCount
    }
}

public extension NutritionSummary {
    // 能量进度
    var energyProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.energyKcal / rec.energy
    }
    
    // 蛋白质进度
    var proteinProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.protein / rec.protein
    }
    
    // 脂肪进度
    var fatProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.fat / rec.fat
    }
    
    // 碳水化合物进度
    var carbohydrateProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.carbohydrate / rec.carbohydrate
    }
    
    // 钠进度
    var sodiumProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.sodium / rec.sodium
    }
    
    // 营养状态评估
    var nutritionStatus: NutritionStatus {
        let energyRatio = energyProgress
        let proteinRatio = proteinProgress
        let fatRatio = fatProgress
        let carbohydrateRatio = carbohydrateProgress
        let sodiumRatio = sodiumProgress
        
        // 简单的评估逻辑
        if energyRatio > 1.2 || fatRatio > 1.3 || sodiumRatio > 1.5 {
            return .excessive
        } else if energyRatio < 0.8 || proteinRatio < 0.8 {
            return .insufficient
        } else {
            return .balanced
        }
    }
}

// MARK: - 营养状态
public enum NutritionStatus: String, CaseIterable {
    case insufficient = "不足"
    case balanced = "均衡"
    case excessive = "过量"
    
    public var color: String {
        switch self {
        case .insufficient: return "orange"
        case .balanced: return "green"
        case .excessive: return "red"
        }
    }
    
    public var icon: String {
        switch self {
        case .insufficient: return "arrow.down.circle.fill"
        case .balanced: return "checkmark.circle.fill"
        case .excessive: return "arrow.up.circle.fill"
        }
    }
}
