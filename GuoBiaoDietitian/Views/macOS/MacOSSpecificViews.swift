/*
macOS 专用视图

Abstract:
为 macOS 平台提供专用的视图组件和交互方式。
*/

import SwiftUI

#if os(macOS)
import AppKit

// MARK: - macOS 工具栏
struct MacOSToolbar: ToolbarContent {
    @EnvironmentObject var nutritionModel: NutritionModel
    @Binding var selection: Panel?
    
    var body: some ToolbarContent {
        ToolbarItemGroup(placement: .navigation) {
            But<PERSON>(action: toggleSidebar) {
                Image(systemName: "sidebar.left")
            }
            .help("显示/隐藏侧边栏")
        }
        
        ToolbarItemGroup(placement: .primaryAction) {
            Button(action: addFoodRecord) {
                Image(systemName: "plus")
            }
            .help("添加食物记录")
            
            Button(action: refreshData) {
                Image(systemName: "arrow.clockwise")
            }
            .help("刷新数据")
            
            Menu {
                Button("导出数据") { exportData() }
                Button("导入数据") { importData() }
                Divider()
                But<PERSON>("设置") { selection = .settings }
            } label: {
                Image(systemName: "ellipsis.circle")
            }
            .help("更多选项")
        }
        
        ToolbarItemGroup(placement: .status) {
            if let summary = nutritionModel.todayNutritionSummary.recommendation {
                HStack(spacing: 8) {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.red)
                        .font(.caption)
                    
                    Text("今日: \(Int(nutritionModel.todayNutritionSummary.totalNutrition.energyKcal))/\(Int(summary.energy)) kcal")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private func toggleSidebar() {
        NSApp.keyWindow?.firstResponder?.tryToPerform(#selector(NSSplitViewController.toggleSidebar(_:)), with: nil)
    }
    
    private func addFoodRecord() {
        selection = .record
    }
    
    private func refreshData() {
        nutritionModel.loadTodayRecords()
        nutritionModel.loadWeeklyRecords()
        nutritionModel.loadMonthlyRecords()
    }
    
    private func exportData() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.json]
        panel.nameFieldStringValue = "nutrition_data.json"
        
        panel.begin { response in
            if response == .OK, let url = panel.url {
                // 实现数据导出逻辑
                exportNutritionData(to: url)
            }
        }
    }
    
    private func importData() {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [.json]
        panel.allowsMultipleSelection = false
        
        panel.begin { response in
            if response == .OK, let url = panel.urls.first {
                // 实现数据导入逻辑
                importNutritionData(from: url)
            }
        }
    }
    
    private func exportNutritionData(to url: URL) {
        // 实现导出逻辑
        print("导出数据到: \(url)")
    }
    
    private func importNutritionData(from url: URL) {
        // 实现导入逻辑
        print("从以下位置导入数据: \(url)")
    }
}

// MARK: - macOS 上下文菜单
struct MacOSContextMenu: View {
    let record: FoodRecord
    @EnvironmentObject var nutritionModel: NutritionModel
    
    var body: some View {
        Group {
            Button("编辑记录") {
                editRecord(record)
            }
            
            Button("复制记录") {
                duplicateRecord(record)
            }
            
            Divider()
            
            Button("查看营养详情") {
                showNutritionDetails(record)
            }
            
            Button("添加到收藏") {
                addToFavorites(record)
            }
            
            Divider()
            
            Button("删除记录") {
                deleteRecord(record)
            }
            .foregroundColor(.red)
        }
    }
    
    private func editRecord(_ record: FoodRecord) {
        // 实现编辑逻辑
        print("编辑记录: \(record.food.name)")
    }
    
    private func duplicateRecord(_ record: FoodRecord) {
        let newRecord = FoodRecord(
            food: record.food,
            amount: record.amount,
            mealType: record.mealType,
            timestamp: Date(),
            notes: record.notes
        )
        nutritionModel.addFoodRecord(newRecord)
    }
    
    private func showNutritionDetails(_ record: FoodRecord) {
        // 实现营养详情显示逻辑
        print("显示营养详情: \(record.food.name)")
    }
    
    private func addToFavorites(_ record: FoodRecord) {
        // 实现添加到收藏逻辑
        print("添加到收藏: \(record.food.name)")
    }
    
    private func deleteRecord(_ record: FoodRecord) {
        nutritionModel.deleteFoodRecord(record)
    }
}

// MARK: - macOS 键盘快捷键
struct MacOSKeyboardShortcuts: View {
    @Binding var selection: Panel?
    @EnvironmentObject var nutritionModel: NutritionModel
    
    var body: some View {
        EmptyView()
            .onReceive(NotificationCenter.default.publisher(for: NSApplication.didBecomeActiveNotification)) { _ in
                setupKeyboardShortcuts()
            }
    }
    
    private func setupKeyboardShortcuts() {
        // 设置全局键盘快捷键
        NSEvent.addLocalMonitorForEvents(matching: .keyDown) { event in
            if event.modifierFlags.contains(.command) {
                switch event.charactersIgnoringModifiers {
                case "1":
                    selection = .overview
                    return nil
                case "2":
                    selection = .record
                    return nil
                case "3":
                    selection = .trends
                    return nil
                case "4":
                    selection = .assistant
                    return nil
                case "5":
                    selection = .settings
                    return nil
                case "r":
                    nutritionModel.loadTodayRecords()
                    return nil
                case "n":
                    selection = .record
                    return nil
                default:
                    break
                }
            }
            return event
        }
    }
}

// MARK: - macOS 窗口管理
struct MacOSWindowManager: View {
    @State private var isFullScreen = false
    
    var body: some View {
        EmptyView()
            .onReceive(NotificationCenter.default.publisher(for: NSWindow.didEnterFullScreenNotification)) { _ in
                isFullScreen = true
            }
            .onReceive(NotificationCenter.default.publisher(for: NSWindow.didExitFullScreenNotification)) { _ in
                isFullScreen = false
            }
    }
}

// MARK: - macOS 偏好设置窗口
struct MacOSPreferencesView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            GeneralPreferencesView()
                .tabItem {
                    Image(systemName: "gearshape")
                    Text("通用")
                }
                .tag(0)
            
            NutritionPreferencesView()
                .tabItem {
                    Image(systemName: "heart")
                    Text("营养")
                }
                .tag(1)
            
            DataPreferencesView()
                .tabItem {
                    Image(systemName: "externaldrive")
                    Text("数据")
                }
                .tag(2)
            
            AdvancedPreferencesView()
                .tabItem {
                    Image(systemName: "slider.horizontal.3")
                    Text("高级")
                }
                .tag(3)
        }
        .frame(width: 500, height: 400)
    }
}

struct GeneralPreferencesView: View {
    @AppStorage("launchAtLogin") private var launchAtLogin = false
    @AppStorage("showMenuBarIcon") private var showMenuBarIcon = true
    @AppStorage("enableNotifications") private var enableNotifications = true
    
    var body: some View {
        Form {
            Section("启动") {
                Toggle("登录时启动", isOn: $launchAtLogin)
                Toggle("显示菜单栏图标", isOn: $showMenuBarIcon)
            }
            
            Section("通知") {
                Toggle("启用通知", isOn: $enableNotifications)
            }
        }
        .padding()
    }
}

struct NutritionPreferencesView: View {
    @AppStorage("dailyCalorieGoal") private var dailyCalorieGoal = 2000.0
    @AppStorage("proteinGoal") private var proteinGoal = 60.0
    @AppStorage("fatGoal") private var fatGoal = 67.0
    @AppStorage("carbGoal") private var carbGoal = 250.0
    
    var body: some View {
        Form {
            Section("每日目标") {
                HStack {
                    Text("卡路里:")
                    Spacer()
                    TextField("", value: $dailyCalorieGoal, format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 100)
                    Text("kcal")
                }
                
                HStack {
                    Text("蛋白质:")
                    Spacer()
                    TextField("", value: $proteinGoal, format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 100)
                    Text("g")
                }
                
                HStack {
                    Text("脂肪:")
                    Spacer()
                    TextField("", value: $fatGoal, format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 100)
                    Text("g")
                }
                
                HStack {
                    Text("碳水化合物:")
                    Spacer()
                    TextField("", value: $carbGoal, format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 100)
                    Text("g")
                }
            }
        }
        .padding()
    }
}

struct DataPreferencesView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("数据管理")
                .font(.headline)
            
            VStack(spacing: 12) {
                Button("导出所有数据") {
                    // 实现导出逻辑
                }
                .buttonStyle(.borderedProminent)
                
                Button("导入数据") {
                    // 实现导入逻辑
                }
                .buttonStyle(.bordered)
                
                Button("清除所有数据") {
                    // 实现清除逻辑
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
            }
            
            Spacer()
        }
        .padding()
    }
}

struct AdvancedPreferencesView: View {
    @AppStorage("enableDebugMode") private var enableDebugMode = false
    @AppStorage("dataRetentionDays") private var dataRetentionDays = 365.0
    
    var body: some View {
        Form {
            Section("调试") {
                Toggle("启用调试模式", isOn: $enableDebugMode)
            }
            
            Section("数据保留") {
                HStack {
                    Text("保留天数:")
                    Spacer()
                    TextField("", value: $dataRetentionDays, format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 100)
                    Text("天")
                }
            }
        }
        .padding()
    }
}

#endif
