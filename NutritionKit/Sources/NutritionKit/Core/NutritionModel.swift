/*
营养管理核心模型

Abstract:
参考 FoodTruckModel 设计的营养管理核心类，提供集中式状态管理和数据绑定。
*/

import Foundation
import SwiftUI
import Combine

@MainActor
public class NutritionModel: ObservableObject {
    // MARK: - Published Properties
    @Published public var userProfile: UserProfile?
    @Published public var todayRecords: [FoodRecord] = []
    @Published public var weeklyRecords: [FoodRecord] = []
    @Published public var monthlyRecords: [FoodRecord] = []
    @Published public var foods: [Food] = []
    @Published public var isLoading = false
    @Published public var errorMessage: String?
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    public init() {
        loadUserProfile()
        loadTodayRecords()
        loadFoods()
        
        // 模拟数据生成 (用于开发和演示)
        generateSampleData()
    }
    
    // MARK: - User Profile Management
    public func loadUserProfile() {
        // 从本地存储加载用户资料
        // 这里先使用模拟数据
        if userProfile == nil {
            userProfile = UserProfile(
                name: "用户",
                gender: .female,
                birthDate: Calendar.current.date(byAdding: .year, value: -30, to: Date()) ?? Date(),
                height: 165,
                weight: 60,
                activityLevel: .moderatelyActive,
                healthGoals: [.maintainWeight, .improveHealth]
            )
        }
    }
    
    public func updateUserProfile(_ profile: UserProfile) {
        var updatedProfile = profile
        updatedProfile.updatedAt = Date()
        userProfile = updatedProfile
        
        // 保存到本地存储
        saveUserProfile(updatedProfile)
    }
    
    private func saveUserProfile(_ profile: UserProfile) {
        // 实现本地存储逻辑
        // 可以使用 UserDefaults、Core Data 或文件系统
    }
    
    // MARK: - Food Records Management
    public func loadTodayRecords() {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        
        todayRecords = loadRecords(from: today, to: tomorrow)
    }
    
    public func loadWeeklyRecords() {
        let calendar = Calendar.current
        let today = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        weeklyRecords = loadRecords(from: weekAgo, to: today)
    }
    
    public func loadMonthlyRecords() {
        let calendar = Calendar.current
        let today = Date()
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: today)!
        
        monthlyRecords = loadRecords(from: monthAgo, to: today)
    }
    
    private func loadRecords(from startDate: Date, to endDate: Date) -> [FoodRecord] {
        // 从本地存储加载记录
        // 这里返回模拟数据
        return []
    }
    
    public func addFoodRecord(_ record: FoodRecord) {
        withAnimation(.spring(response: 0.4, dampingFraction: 1)) {
            todayRecords.append(record)
        }
        
        // 保存到本地存储
        saveFoodRecord(record)
        
        // 更新相关记录集合
        if record.timestamp >= Calendar.current.date(byAdding: .day, value: -7, to: Date())! {
            weeklyRecords.append(record)
        }
        if record.timestamp >= Calendar.current.date(byAdding: .month, value: -1, to: Date())! {
            monthlyRecords.append(record)
        }
    }
    
    public func deleteFoodRecord(_ record: FoodRecord) {
        withAnimation(.spring(response: 0.4, dampingFraction: 1)) {
            todayRecords.removeAll { $0.id == record.id }
            weeklyRecords.removeAll { $0.id == record.id }
            monthlyRecords.removeAll { $0.id == record.id }
        }
        
        // 从本地存储删除
        deleteFoodRecordFromStorage(record.id)
    }
    
    private func saveFoodRecord(_ record: FoodRecord) {
        // 实现本地存储逻辑
    }
    
    private func deleteFoodRecordFromStorage(_ id: UUID) {
        // 实现删除逻辑
    }
    
    // MARK: - Food Management
    public func loadFoods() {
        // 加载食物数据库
        // 这里使用模拟数据
        foods = Food.sampleFoods
    }
    
    public func addFood(_ food: Food) {
        foods.append(food)
        saveFoodToDatabase(food)
    }
    
    private func saveFoodToDatabase(_ food: Food) {
        // 保存到食物数据库
    }
    
    // MARK: - Data Binding (参考 FoodTruckModel)
    public func foodRecordBinding(for id: FoodRecord.ID) -> Binding<FoodRecord> {
        Binding<FoodRecord> {
            guard let index = self.todayRecords.firstIndex(where: { $0.id == id }) else {
                fatalError("FoodRecord with id \(id) not found")
            }
            return self.todayRecords[index]
        } set: { newValue in
            guard let index = self.todayRecords.firstIndex(where: { $0.id == id }) else {
                fatalError("FoodRecord with id \(id) not found")
            }
            self.todayRecords[index] = newValue
        }
    }
    
    public func foodBinding(for id: Food.ID) -> Binding<Food> {
        Binding<Food> {
            guard let index = self.foods.firstIndex(where: { $0.id == id }) else {
                fatalError("Food with id \(id) not found")
            }
            return self.foods[index]
        } set: { newValue in
            guard let index = self.foods.firstIndex(where: { $0.id == id }) else {
                fatalError("Food with id \(id) not found")
            }
            self.foods[index] = newValue
        }
    }
    
    // MARK: - Nutrition Analysis
    public var todayNutritionSummary: NutritionSummary {
        calculateNutritionSummary(for: todayRecords)
    }
    
    public var weeklyNutritionSummary: NutritionSummary {
        calculateNutritionSummary(for: weeklyRecords)
    }
    
    public var monthlyNutritionSummary: NutritionSummary {
        calculateNutritionSummary(for: monthlyRecords)
    }
    
    public var dailyRecommendation: ChineseDietaryStandards.DailyRecommendation? {
        guard let profile = userProfile else { return nil }
        return ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: profile.gender,
            activityLevel: profile.activityLevel,
            age: profile.age,
            weight: profile.weight,
            height: profile.height
        )
    }
    
    private func calculateNutritionSummary(for records: [FoodRecord]) -> NutritionSummary {
        let totalNutrition = records.reduce(NutritionFacts.empty) { result, record in
            result + record.actualNutrition
        }
        
        let mealDistribution = Dictionary(grouping: records, by: { $0.mealType })
            .mapValues { records in
                records.reduce(0) { $0 + $1.actualNutrition.energyKcal }
            }
        
        return NutritionSummary(
            totalNutrition: totalNutrition,
            recommendation: dailyRecommendation,
            mealDistribution: mealDistribution,
            recordsCount: records.count
        )
    }
    
    // MARK: - Sample Data Generation
    private func generateSampleData() {
        // 生成一些示例食物记录用于演示
        Task(priority: .background) {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
            
            await MainActor.run {
                if todayRecords.isEmpty {
                    let sampleRecords = FoodRecord.sampleRecords
                    for record in sampleRecords {
                        addFoodRecord(record)
                    }
                }
            }
        }
    }
}

// MARK: - Preview Support
public extension NutritionModel {
    static let preview: NutritionModel = {
        let model = NutritionModel()
        return model
    }()
}
