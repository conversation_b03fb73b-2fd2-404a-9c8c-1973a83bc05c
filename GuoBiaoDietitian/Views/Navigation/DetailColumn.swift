/*
详情列视图

Abstract:
导航分屏视图的详情列，根据选择的面板显示相应内容。
*/

import SwiftUI
import NutritionKit

struct DetailColumn: View {
    @Binding var selection: Panel?
    @ObservedObject var model: NutritionModel
    @EnvironmentObject var healthKitManager: HealthKitManager
    
    var body: some View {
        Group {
            switch selection {
            case .overview:
                NutritionOverviewView()
                
            case .record:
                FoodRecordView()
                
            case .trends:
                TrendAnalysisView()
                
            case .assistant:
                AIAssistantView()
                
            case .settings:
                SettingsView()
                
            case .none:
                WelcomeView()
            }
        }
        .environmentObject(model)
    }
}

struct WelcomeView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.accentColor)
            
            Text("欢迎使用国标营养师")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("基于中国国标的专业营养管理应用")
                .font(.title2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            VStack(alignment: .leading, spacing: 12) {
                FeatureRow(
                    icon: "chart.pie.fill",
                    title: "营养概览",
                    description: "实时查看营养摄入状态"
                )
                
                FeatureRow(
                    icon: "plus.circle.fill",
                    title: "智能记录",
                    description: "拍照识别，快速记录食物"
                )
                
                FeatureRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "趋势分析",
                    description: "科学分析营养摄入趋势"
                )
                
                FeatureRow(
                    icon: "brain.head.profile",
                    title: "AI 助手",
                    description: "个性化营养建议"
                )
            }
            .padding(.top, 20)
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal)
    }
}

struct DetailColumn_Previews: PreviewProvider {
    struct Preview: View {
        @State private var selection: Panel? = .overview
        
        var body: some View {
            NavigationSplitView {
                Sidebar(selection: $selection)
                    .environmentObject(NutritionModel.preview)
                    .environmentObject(HealthKitManager())
            } detail: {
                NavigationStack {
                    DetailColumn(selection: $selection, model: NutritionModel.preview)
                        .environmentObject(HealthKitManager())
                }
            }
        }
    }
    
    static var previews: some View {
        Preview()
    }
}
