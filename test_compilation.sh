#!/bin/bash

# 编译测试脚本
# 用于快速检查项目是否可以编译

echo "🚀 开始编译测试..."

# 设置项目路径
PROJECT_PATH="GuoBiaoDietitian.xcodeproj"
SCHEME="GuoBiaoDietitian"

# 检查项目文件是否存在
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 错误: 找不到项目文件 $PROJECT_PATH"
    exit 1
fi

echo "📱 正在编译 iOS 版本..."

# 编译 iOS 版本
xcodebuild -project "$PROJECT_PATH" \
           -scheme "$SCHEME" \
           -destination 'platform=iOS Simulator,name=iPhone 15' \
           clean build \
           -quiet

if [ $? -eq 0 ]; then
    echo "✅ iOS 编译成功!"
else
    echo "❌ iOS 编译失败"
    echo "🔍 尝试获取详细错误信息..."
    
    # 重新编译并显示详细错误
    xcodebuild -project "$PROJECT_PATH" \
               -scheme "$SCHEME" \
               -destination 'platform=iOS Simulator,name=iPhone 15' \
               build
    exit 1
fi

echo "🎉 编译测试完成!"
echo ""
echo "📋 下一步建议:"
echo "1. 在 Xcode 中打开项目: open $PROJECT_PATH"
echo "2. 选择 iPhone 模拟器"
echo "3. 点击 Run 按钮测试应用"
echo ""
echo "🔧 如果遇到问题:"
echo "- 检查 Xcode 版本是否为最新"
echo "- 确保所有文件都已添加到项目中"
echo "- 清理构建文件夹: Product → Clean Build Folder"
