/*
示例数据

Abstract:
提供用于开发、测试和演示的示例数据。
*/

import Foundation

// MARK: - Food Sample Data
public extension Food {
    static let sampleFoods: [Food] = [
        // 谷薯类
        Food(
            name: "白米饭",
            category: .grains,
            nutritionFacts: NutritionFacts(
                energy: 485, // kJ per 100g
                protein: 2.6,
                fat: 0.3,
                carbohydrate: 25.9,
                sodium: 1.0,
                dietaryFiber: 0.3
            ),
            servingSize: 150,
            source: .database
        ),
        
        Food(
            name: "全麦面包",
            category: .grains,
            nutritionFacts: NutritionFacts(
                energy: 1050, // kJ per 100g
                protein: 9.0,
                fat: 4.0,
                carbohydrate: 43.0,
                sodium: 450.0,
                dietaryFiber: 7.0
            ),
            servingSize: 60,
            source: .database
        ),
        
        // 蔬菜类
        Food(
            name: "西兰花",
            category: .vegetables,
            nutritionFacts: NutritionFacts(
                energy: 142, // kJ per 100g
                protein: 4.1,
                fat: 0.4,
                carbohydrate: 4.3,
                sodium: 15.0,
                dietaryFiber: 2.6,
                vitaminC: 51.0,
                calcium: 67.0
            ),
            servingSize: 100,
            source: .database
        ),
        
        Food(
            name: "胡萝卜",
            category: .vegetables,
            nutritionFacts: NutritionFacts(
                energy: 172, // kJ per 100g
                protein: 1.0,
                fat: 0.2,
                carbohydrate: 8.8,
                sodium: 25.0,
                dietaryFiber: 3.2,
                vitaminA: 1890.0
            ),
            servingSize: 80,
            source: .database
        ),
        
        // 水果类
        Food(
            name: "苹果",
            category: .fruits,
            nutritionFacts: NutritionFacts(
                energy: 218, // kJ per 100g
                protein: 0.2,
                fat: 0.2,
                carbohydrate: 13.8,
                sodium: 1.0,
                dietaryFiber: 2.4,
                vitaminC: 4.0
            ),
            servingSize: 150,
            source: .database
        ),
        
        Food(
            name: "香蕉",
            category: .fruits,
            nutritionFacts: NutritionFacts(
                energy: 377, // kJ per 100g
                protein: 1.4,
                fat: 0.2,
                carbohydrate: 22.0,
                sodium: 1.0,
                dietaryFiber: 1.2,
                potassium: 256.0
            ),
            servingSize: 120,
            source: .database
        ),
        
        // 肉类
        Food(
            name: "鸡胸肉",
            category: .meat,
            nutritionFacts: NutritionFacts(
                energy: 485, // kJ per 100g
                protein: 23.3,
                fat: 1.9,
                carbohydrate: 0.0,
                sodium: 63.0,
                iron: 1.0,
                zinc: 0.9
            ),
            servingSize: 100,
            source: .database
        ),
        
        Food(
            name: "瘦猪肉",
            category: .meat,
            nutritionFacts: NutritionFacts(
                energy: 610, // kJ per 100g
                protein: 20.3,
                fat: 6.2,
                carbohydrate: 0.0,
                sodium: 65.0,
                iron: 3.0,
                zinc: 2.3
            ),
            servingSize: 80,
            source: .database
        ),
        
        // 鱼虾类
        Food(
            name: "三文鱼",
            category: .fish,
            nutritionFacts: NutritionFacts(
                energy: 590, // kJ per 100g
                protein: 22.0,
                fat: 6.3,
                carbohydrate: 0.0,
                sodium: 59.0,
                vitaminD: 11.0
            ),
            servingSize: 100,
            source: .database
        ),
        
        // 蛋类
        Food(
            name: "鸡蛋",
            category: .eggs,
            nutritionFacts: NutritionFacts(
                energy: 627, // kJ per 100g
                protein: 13.3,
                fat: 8.8,
                carbohydrate: 2.8,
                sodium: 131.0,
                vitaminA: 234.0,
                vitaminD: 2.0
            ),
            servingSize: 50,
            source: .database
        ),
        
        // 奶类
        Food(
            name: "牛奶",
            category: .dairy,
            nutritionFacts: NutritionFacts(
                energy: 272, // kJ per 100g
                protein: 3.4,
                fat: 3.7,
                carbohydrate: 5.0,
                sodium: 37.0,
                calcium: 104.0
            ),
            servingSize: 250,
            source: .database
        ),
        
        // 坚果类
        Food(
            name: "核桃",
            category: .nuts,
            nutritionFacts: NutritionFacts(
                energy: 2738, // kJ per 100g
                protein: 15.2,
                fat: 58.8,
                carbohydrate: 19.1,
                sodium: 6.0,
                dietaryFiber: 9.5
            ),
            servingSize: 15,
            source: .database
        )
    ]
}

// MARK: - FoodRecord Sample Data
public extension FoodRecord {
    static let sampleRecords: [FoodRecord] = [
        // 早餐
        FoodRecord(
            food: Food.sampleFoods[1], // 全麦面包
            amount: 60,
            mealType: .breakfast,
            timestamp: Calendar.current.date(byAdding: .hour, value: -10, to: Date()) ?? Date()
        ),
        
        FoodRecord(
            food: Food.sampleFoods[9], // 鸡蛋
            amount: 50,
            mealType: .breakfast,
            timestamp: Calendar.current.date(byAdding: .hour, value: -10, to: Date()) ?? Date()
        ),
        
        FoodRecord(
            food: Food.sampleFoods[10], // 牛奶
            amount: 250,
            mealType: .breakfast,
            timestamp: Calendar.current.date(byAdding: .hour, value: -10, to: Date()) ?? Date()
        ),
        
        // 午餐
        FoodRecord(
            food: Food.sampleFoods[0], // 白米饭
            amount: 150,
            mealType: .lunch,
            timestamp: Calendar.current.date(byAdding: .hour, value: -5, to: Date()) ?? Date()
        ),
        
        FoodRecord(
            food: Food.sampleFoods[6], // 鸡胸肉
            amount: 100,
            mealType: .lunch,
            timestamp: Calendar.current.date(byAdding: .hour, value: -5, to: Date()) ?? Date()
        ),
        
        FoodRecord(
            food: Food.sampleFoods[2], // 西兰花
            amount: 100,
            mealType: .lunch,
            timestamp: Calendar.current.date(byAdding: .hour, value: -5, to: Date()) ?? Date()
        ),
        
        // 下午加餐
        FoodRecord(
            food: Food.sampleFoods[4], // 苹果
            amount: 150,
            mealType: .afternoonSnack,
            timestamp: Calendar.current.date(byAdding: .hour, value: -2, to: Date()) ?? Date()
        ),
        
        FoodRecord(
            food: Food.sampleFoods[11], // 核桃
            amount: 15,
            mealType: .afternoonSnack,
            timestamp: Calendar.current.date(byAdding: .hour, value: -2, to: Date()) ?? Date()
        )
    ]
}

// MARK: - UserProfile Sample Data
public extension UserProfile {
    static let sampleProfiles: [UserProfile] = [
        UserProfile(
            name: "张小明",
            gender: .male,
            birthDate: Calendar.current.date(byAdding: .year, value: -28, to: Date()) ?? Date(),
            height: 175,
            weight: 70,
            activityLevel: .moderatelyActive,
            healthGoals: [.maintainWeight, .improveHealth]
        ),
        
        UserProfile(
            name: "李小红",
            gender: .female,
            birthDate: Calendar.current.date(byAdding: .year, value: -25, to: Date()) ?? Date(),
            height: 162,
            weight: 55,
            activityLevel: .lightlyActive,
            healthGoals: [.weightLoss, .improveHealth]
        ),
        
        UserProfile(
            name: "王大力",
            gender: .male,
            birthDate: Calendar.current.date(byAdding: .year, value: -35, to: Date()) ?? Date(),
            height: 180,
            weight: 85,
            activityLevel: .veryActive,
            healthGoals: [.buildMuscle, .improvePerformance]
        )
    ]
}
