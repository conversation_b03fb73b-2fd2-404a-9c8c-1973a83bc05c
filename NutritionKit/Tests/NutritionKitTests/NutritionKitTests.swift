/*
NutritionKit 测试

Abstract:
NutritionKit 核心功能的单元测试。
*/

import XCTest
@testable import NutritionKit

final class NutritionKitTests: XCTestCase {
    
    // MARK: - NutritionFacts Tests
    func testNutritionFactsEnergyConversion() {
        let nutrition = NutritionFacts(
            energy: 4184, // 4184 kJ = 1000 kcal
            protein: 10,
            fat: 10,
            carbohydrate: 10,
            sodium: 100
        )
        
        XCTAssertEqual(nutrition.energyKcal, 1000, accuracy: 0.1)
    }
    
    func testNutritionFactsAddition() {
        let nutrition1 = NutritionFacts(
            energy: 1000,
            protein: 10,
            fat: 5,
            carbohydrate: 20,
            sodium: 100
        )
        
        let nutrition2 = NutritionFacts(
            energy: 2000,
            protein: 15,
            fat: 10,
            carbohydrate: 30,
            sodium: 200
        )
        
        let total = nutrition1 + nutrition2
        
        XCTAssertEqual(total.energy, 3000)
        XCTAssertEqual(total.protein, 25)
        XCTAssertEqual(total.fat, 15)
        XCTAssertEqual(total.carbohydrate, 50)
        XCTAssertEqual(total.sodium, 300)
    }
    
    // MARK: - UserProfile Tests
    func testUserProfileBMICalculation() {
        let profile = UserProfile(
            name: "Test User",
            gender: .male,
            birthDate: Calendar.current.date(byAdding: .year, value: -30, to: Date()) ?? Date(),
            height: 175, // cm
            weight: 70, // kg
            activityLevel: .moderatelyActive
        )
        
        let expectedBMI = 70 / (1.75 * 1.75) // 22.86
        XCTAssertEqual(profile.bmi, expectedBMI, accuracy: 0.01)
        XCTAssertEqual(profile.bmiCategory, .normal)
    }
    
    func testUserProfileAgeCalculation() {
        let birthDate = Calendar.current.date(byAdding: .year, value: -25, to: Date()) ?? Date()
        let profile = UserProfile(
            name: "Test User",
            gender: .female,
            birthDate: birthDate,
            height: 165,
            weight: 55,
            activityLevel: .lightlyActive
        )
        
        XCTAssertEqual(profile.age, 25)
    }
    
    func testUserProfileBMRCalculation() {
        // 测试男性BMR计算
        let maleProfile = UserProfile(
            name: "Male User",
            gender: .male,
            birthDate: Calendar.current.date(byAdding: .year, value: -30, to: Date()) ?? Date(),
            height: 175,
            weight: 70,
            activityLevel: .moderatelyActive
        )
        
        // BMR = 10 * 70 + 6.25 * 175 - 5 * 30 + 5 = 1663.75
        let expectedMaleBMR = 10 * 70 + 6.25 * 175 - 5 * 30 + 5
        XCTAssertEqual(maleProfile.bmr, expectedMaleBMR, accuracy: 0.1)
        
        // 测试女性BMR计算
        let femaleProfile = UserProfile(
            name: "Female User",
            gender: .female,
            birthDate: Calendar.current.date(byAdding: .year, value: -25, to: Date()) ?? Date(),
            height: 165,
            weight: 55,
            activityLevel: .lightlyActive
        )
        
        // BMR = 10 * 55 + 6.25 * 165 - 5 * 25 - 161 = 1296.25
        let expectedFemaleBMR = 10 * 55 + 6.25 * 165 - 5 * 25 - 161
        XCTAssertEqual(femaleProfile.bmr, expectedFemaleBMR, accuracy: 0.1)
    }
    
    // MARK: - FoodRecord Tests
    func testFoodRecordNutritionCalculation() {
        let food = Food(
            name: "Test Food",
            category: .grains,
            nutritionFacts: NutritionFacts(
                energy: 1000, // per 100g
                protein: 10,  // per 100g
                fat: 5,       // per 100g
                carbohydrate: 20, // per 100g
                sodium: 100   // per 100g
            ),
            source: .database
        )
        
        let record = FoodRecord(
            food: food,
            amount: 200, // 200g
            mealType: .lunch
        )
        
        let actualNutrition = record.actualNutrition
        
        // 200g should be 2x the nutrition facts
        XCTAssertEqual(actualNutrition.energy, 2000)
        XCTAssertEqual(actualNutrition.protein, 20)
        XCTAssertEqual(actualNutrition.fat, 10)
        XCTAssertEqual(actualNutrition.carbohydrate, 40)
        XCTAssertEqual(actualNutrition.sodium, 200)
    }
    
    // MARK: - ChineseDietaryStandards Tests
    func testDailyRecommendationCalculation() {
        let recommendation = ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: .male,
            activityLevel: .moderatelyActive,
            age: 30,
            weight: 70,
            height: 175
        )
        
        // 验证推荐量是否合理
        XCTAssertGreaterThan(recommendation.energy, 1500)
        XCTAssertLessThan(recommendation.energy, 3000)
        XCTAssertGreaterThan(recommendation.protein, 50)
        XCTAssertLessThan(recommendation.protein, 150)
        XCTAssertEqual(recommendation.sodium, 2000)
    }
    
    func testNutritionAssessment() {
        // 测试营养素摄入评估
        let adequateStatus = ChineseDietaryStandards.NutritionAssessment.assessNutrientIntake(
            actual: 100,
            recommended: 100,
            nutrient: "protein"
        )
        XCTAssertEqual(adequateStatus, .adequate)
        
        let insufficientStatus = ChineseDietaryStandards.NutritionAssessment.assessNutrientIntake(
            actual: 70,
            recommended: 100,
            nutrient: "protein"
        )
        XCTAssertEqual(insufficientStatus, .insufficient)
        
        let excessiveStatus = ChineseDietaryStandards.NutritionAssessment.assessNutrientIntake(
            actual: 130,
            recommended: 100,
            nutrient: "protein"
        )
        XCTAssertEqual(excessiveStatus, .excessive)
    }
    
    // MARK: - NutritionSummary Tests
    func testNutritionSummaryProgress() {
        let totalNutrition = NutritionFacts(
            energy: 8400, // kJ (2000 kcal)
            protein: 60,
            fat: 60,
            carbohydrate: 250,
            sodium: 1500
        )
        
        let recommendation = ChineseDietaryStandards.DailyRecommendation(
            energy: 2000, // kcal
            protein: 60,
            fat: 67,
            carbohydrate: 250,
            sodium: 2000,
            dietaryFiber: 25,
            water: 2000
        )
        
        let summary = NutritionSummary(
            totalNutrition: totalNutrition,
            recommendation: recommendation
        )
        
        XCTAssertEqual(summary.energyProgress, 1.0, accuracy: 0.01)
        XCTAssertEqual(summary.proteinProgress, 1.0, accuracy: 0.01)
        XCTAssertEqual(summary.sodiumProgress, 0.75, accuracy: 0.01)
    }
}
