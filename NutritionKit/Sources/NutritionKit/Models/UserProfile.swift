/*
用户资料模型

Abstract:
定义用户个人信息、健康目标等相关数据模型。
*/

import Foundation

// MARK: - 用户资料
public struct UserProfile: Codable, Identifiable {
    public let id: UUID
    public var name: String
    public var gender: Gender
    public var birthDate: Date
    public var height: Double // cm
    public var weight: Double // kg
    public var activityLevel: ActivityLevel
    public var healthGoals: [HealthGoal]
    public var medicalConditions: [String]
    public var allergies: [String]
    public var createdAt: Date
    public var updatedAt: Date
    
    public init(
        id: UUID = UUID(),
        name: String,
        gender: Gender,
        birthDate: Date,
        height: Double,
        weight: Double,
        activityLevel: ActivityLevel,
        healthGoals: [HealthGoal] = [],
        medicalConditions: [String] = [],
        allergies: [String] = [],
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.gender = gender
        self.birthDate = birthDate
        self.height = height
        self.weight = weight
        self.activityLevel = activityLevel
        self.healthGoals = healthGoals
        self.medicalConditions = medicalConditions
        self.allergies = allergies
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

public extension UserProfile {
    // 计算年龄
    var age: Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
        return ageComponents.year ?? 0
    }
    
    // 计算BMI
    var bmi: Double {
        let heightInMeters = height / 100.0
        return weight / (heightInMeters * heightInMeters)
    }
    
    // BMI分类
    var bmiCategory: BMICategory {
        switch bmi {
        case ..<18.5:
            return .underweight
        case 18.5..<24:
            return .normal
        case 24..<28:
            return .overweight
        default:
            return .obese
        }
    }
    
    // 基础代谢率 (BMR) - 使用 Mifflin-St Jeor 公式
    var bmr: Double {
        switch gender {
        case .male:
            return 10 * weight + 6.25 * height - 5 * Double(age) + 5
        case .female:
            return 10 * weight + 6.25 * height - 5 * Double(age) - 161
        }
    }
    
    // 总日消耗能量 (TDEE)
    var tdee: Double {
        return bmr * activityLevel.multiplier
    }
}

// MARK: - 性别
public extension UserProfile {
    enum Gender: String, CaseIterable, Codable {
        case male = "男"
        case female = "女"
        
        public var displayName: String {
            return self.rawValue
        }
    }
}

// MARK: - 活动水平
public extension UserProfile {
    enum ActivityLevel: String, CaseIterable, Codable {
        case sedentary = "久坐少动"
        case lightlyActive = "轻度活动"
        case moderatelyActive = "中度活动"
        case veryActive = "重度活动"
        case extraActive = "极重度活动"
        
        public var displayName: String {
            return self.rawValue
        }
        
        public var description: String {
            switch self {
            case .sedentary:
                return "很少或不运动，主要是坐着工作"
            case .lightlyActive:
                return "轻度运动，每周1-3次"
            case .moderatelyActive:
                return "中度运动，每周3-5次"
            case .veryActive:
                return "重度运动，每周6-7次"
            case .extraActive:
                return "极重度运动，每天2次或体力劳动"
            }
        }
        
        public var multiplier: Double {
            switch self {
            case .sedentary: return 1.2
            case .lightlyActive: return 1.375
            case .moderatelyActive: return 1.55
            case .veryActive: return 1.725
            case .extraActive: return 1.9
            }
        }
    }
}

// MARK: - BMI分类
public enum BMICategory: String, CaseIterable {
    case underweight = "偏瘦"
    case normal = "正常"
    case overweight = "超重"
    case obese = "肥胖"
    
    public var displayName: String {
        return self.rawValue
    }
    
    public var color: String {
        switch self {
        case .underweight: return "blue"
        case .normal: return "green"
        case .overweight: return "orange"
        case .obese: return "red"
        }
    }
    
    public var description: String {
        switch self {
        case .underweight:
            return "BMI < 18.5，建议增加营养摄入"
        case .normal:
            return "BMI 18.5-23.9，保持良好状态"
        case .overweight:
            return "BMI 24-27.9，建议控制饮食"
        case .obese:
            return "BMI ≥ 28，建议咨询医生"
        }
    }
}

// MARK: - 健康目标
public enum HealthGoal: String, CaseIterable, Codable {
    case weightLoss = "减重"
    case weightGain = "增重"
    case maintainWeight = "维持体重"
    case buildMuscle = "增肌"
    case improveHealth = "改善健康"
    case manageDisease = "疾病管理"
    case improvePerformance = "提升运动表现"
    
    public var displayName: String {
        return self.rawValue
    }
    
    public var icon: String {
        switch self {
        case .weightLoss: return "arrow.down.circle"
        case .weightGain: return "arrow.up.circle"
        case .maintainWeight: return "equal.circle"
        case .buildMuscle: return "figure.strengthtraining.traditional"
        case .improveHealth: return "heart.circle"
        case .manageDisease: return "cross.circle"
        case .improvePerformance: return "bolt.circle"
        }
    }
    
    public var description: String {
        switch self {
        case .weightLoss:
            return "通过合理饮食和运动减少体重"
        case .weightGain:
            return "健康增重，增加肌肉量"
        case .maintainWeight:
            return "保持当前体重，维持健康状态"
        case .buildMuscle:
            return "增加肌肉量，提高身体素质"
        case .improveHealth:
            return "改善整体健康状况"
        case .manageDisease:
            return "通过饮食管理慢性疾病"
        case .improvePerformance:
            return "优化营养以提升运动表现"
        }
    }
}
