/*
导航面板定义

Abstract:
定义应用中的主要导航面板，参考 Food Truck 的 Panel 设计。
*/

import Foundation

/// 应用中的主要导航面板
enum Panel: String, CaseIterable, Identifiable {
    case overview = "营养概览"
    case record = "记录食物"
    case trends = "趋势分析"
    case assistant = "AI 助手"
    case settings = "设置"
    
    var id: String { rawValue }
    
    var displayName: String {
        return rawValue
    }
    
    var icon: String {
        switch self {
        case .overview:
            return "chart.pie.fill"
        case .record:
            return "plus.circle.fill"
        case .trends:
            return "chart.line.uptrend.xyaxis"
        case .assistant:
            return "brain.head.profile"
        case .settings:
            return "gearshape.fill"
        }
    }
    
    var description: String {
        switch self {
        case .overview:
            return "查看今日营养摄入概览和健康状态"
        case .record:
            return "记录食物摄入，支持拍照识别和手动输入"
        case .trends:
            return "分析营养摄入趋势和健康数据变化"
        case .assistant:
            return "AI 营养助手，提供个性化建议"
        case .settings:
            return "个人设置和应用配置"
        }
    }
}
