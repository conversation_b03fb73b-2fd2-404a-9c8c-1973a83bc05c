/*
应用主视图

Abstract:
应用的根视图，使用现代的 NavigationSplitView 架构，参考 Food Truck 设计。
*/

import SwiftUI
import NutritionKit
import os

/// 应用的根视图
///
/// 这个视图是 ``GuoBiaoDietitianApp`` 场景中的根视图。
/// 在 macOS 和 iPadOS 上显示分屏导航视图，在 iOS 设备上显示导航堆栈作为应用的主界面。
struct ContentView: View {
    /// 应用的营养模型，由包含的场景传入
    @ObservedObject var model: NutritionModel
    /// HealthKit 管理器，由包含的场景传入
    @ObservedObject var healthKitManager: HealthKitManager

    @State private var selection: Panel? = Panel.overview
    @State private var path = NavigationPath()

    #if os(iOS)
    @Environment(\.scenePhase) private var scenePhase
    #endif

    /// 视图主体
    ///
    /// 这个视图嵌入了一个 [`NavigationSplitView`](https://developer.apple.com/documentation/swiftui/navigationsplitview)，
    /// 在左列显示 ``Sidebar`` 视图，在详情列显示一个 [`NavigationStack`](https://developer.apple.com/documentation/swiftui/navigationstack)，
    /// 包含 ``DetailColumn``，在 macOS 和 iPadOS 上。
    /// 在 iOS 上，[`NavigationSplitView`](https://developer.apple.com/documentation/swiftui/navigationsplitview)
    /// 显示一个以 ``Sidebar`` 视图为根的导航堆栈。
    var body: some View {
        NavigationSplitView {
            Sidebar(selection: $selection)
        } detail: {
            NavigationStack(path: $path) {
                DetailColumn(selection: $selection, model: model)
            }
        }
        .onChange(of: selection) { _ in
            path.removeLast(path.count)
        }
        .environmentObject(model)
        .environmentObject(healthKitManager)
        .onAppear {
            requestHealthKitPermissions()
        }
        #if os(macOS)
        .frame(minWidth: 800, minHeight: 600)
        #elseif os(iOS)
        .onChange(of: scenePhase) { newValue in
            // 当视图变为活跃状态时，重新加载数据
            if newValue == .active {
                model.loadTodayRecords()
                healthKitManager.loadHealthData()
            }
        }
        .onOpenURL { url in
            let urlLogger = Logger(subsystem: "com.borealbit.guobiaodietitian", category: "url")
            urlLogger.log("Received URL: \(url, privacy: .public)")

            // 处理深度链接
            handleDeepLink(url)
        }
        #endif
    }

    private func requestHealthKitPermissions() {
        healthKitManager.requestAuthorization { success in
            if success {
                print("HealthKit authorization granted")
            } else {
                print("HealthKit authorization denied")
            }
        }
    }

    #if os(iOS)
    private func handleDeepLink(_ url: URL) {
        // 处理应用内深度链接
        // 例如：nutritionist://record/food 或 nutritionist://overview
        guard url.scheme == "nutritionist" else { return }

        var newPath = NavigationPath()

        switch url.host {
        case "overview":
            selection = .overview
        case "record":
            selection = .record
        case "trends":
            selection = .trends
        case "assistant":
            selection = .assistant
        case "settings":
            selection = .settings
        default:
            selection = .overview
        }

        path = newPath
    }
    #endif
}

struct ContentView_Previews: PreviewProvider {
    struct Preview: View {
        @StateObject private var model = NutritionModel.preview
        @StateObject private var healthKitManager = HealthKitManager()

        var body: some View {
            ContentView(model: model, healthKitManager: healthKitManager)
        }
    }

    static var previews: some View {
        Preview()
    }
}

#Preview {
    ContentView()
        .environmentObject(NutritionManager())
        .environmentObject(HealthKitManager())
}
