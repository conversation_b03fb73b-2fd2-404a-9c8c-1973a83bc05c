/*
应用主视图

Abstract:
应用的根视图，使用现代的 NavigationSplitView 架构，参考 Food Truck 设计。
*/

import SwiftUI
import os

// MARK: - Panel 枚举定义
/// 应用中的主要导航面板
enum Panel: String, CaseIterable, Identifiable {
    case overview = "营养概览"
    case record = "记录食物"
    case trends = "趋势分析"
    case assistant = "AI 助手"
    case settings = "设置"

    var id: String { rawValue }

    var displayName: String {
        return rawValue
    }

    var icon: String {
        switch self {
        case .overview:
            return "chart.pie.fill"
        case .record:
            return "plus.circle.fill"
        case .trends:
            return "chart.line.uptrend.xyaxis"
        case .assistant:
            return "brain.head.profile"
        case .settings:
            return "gearshape.fill"
        }
    }

    var description: String {
        switch self {
        case .overview:
            return "查看今日营养摄入概览和健康状态"
        case .record:
            return "记录食物摄入，支持拍照识别和手动输入"
        case .trends:
            return "分析营养摄入趋势和健康数据变化"
        case .assistant:
            return "AI 营养助手，提供个性化建议"
        case .settings:
            return "个人设置和应用配置"
        }
    }
}

/// 应用的根视图
///
/// 这个视图是 ``GuoBiaoDietitianApp`` 场景中的根视图。
/// 在 macOS 和 iPadOS 上显示分屏导航视图，在 iOS 设备上显示导航堆栈作为应用的主界面。
struct ContentView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @EnvironmentObject var healthKitManager: HealthKitManager

    @State private var selection: Panel? = Panel.overview
    @State private var path = NavigationPath()

    #if os(iOS)
    @Environment(\.scenePhase) private var scenePhase
    #endif

    /// 视图主体
    ///
    /// 这个视图嵌入了一个 [`NavigationSplitView`](https://developer.apple.com/documentation/swiftui/navigationsplitview)，
    /// 在左列显示 ``Sidebar`` 视图，在详情列显示一个 [`NavigationStack`](https://developer.apple.com/documentation/swiftui/navigationstack)，
    /// 包含 ``DetailColumn``，在 macOS 和 iPadOS 上。
    /// 在 iOS 上，[`NavigationSplitView`](https://developer.apple.com/documentation/swiftui/navigationsplitview)
    /// 显示一个以 ``Sidebar`` 视图为根的导航堆栈。
    var body: some View {
        NavigationSplitView {
            Sidebar(selection: $selection)
        } detail: {
            NavigationStack(path: $path) {
                DetailColumn(selection: $selection)
            }
        }
        .onChange(of: selection) { _ in
            path.removeLast(path.count)
        }
        .onAppear {
            requestHealthKitPermissions()
        }
        #if os(macOS)
        .frame(minWidth: 800, minHeight: 600)
        #elseif os(iOS)
        .onChange(of: scenePhase) { newValue in
            // 当视图变为活跃状态时，重新加载数据
            if newValue == .active {
                nutritionManager.loadTodayRecords()
                healthKitManager.loadHealthData()
            }
        }
        .onOpenURL { url in
            let urlLogger = Logger(subsystem: "com.borealbit.guobiaodietitian", category: "url")
            urlLogger.log("Received URL: \(url, privacy: .public)")

            // 处理深度链接
            handleDeepLink(url)
        }
        #endif
    }

    private func requestHealthKitPermissions() {
        healthKitManager.requestAuthorization { success in
            if success {
                print("HealthKit authorization granted")
            } else {
                print("HealthKit authorization denied")
            }
        }
    }

    #if os(iOS)
    private func handleDeepLink(_ url: URL) {
        // 处理应用内深度链接
        // 例如：nutritionist://record/food 或 nutritionist://overview
        guard url.scheme == "nutritionist" else { return }

        var newPath = NavigationPath()

        switch url.host {
        case "overview":
            selection = .overview
        case "record":
            selection = .record
        case "trends":
            selection = .trends
        case "assistant":
            selection = .assistant
        case "settings":
            selection = .settings
        default:
            selection = .overview
        }

        path = newPath
    }
    #endif
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(NutritionManager())
            .environmentObject(HealthKitManager())
    }
}

#Preview {
    ContentView()
        .environmentObject(NutritionManager())
        .environmentObject(HealthKitManager())
}
